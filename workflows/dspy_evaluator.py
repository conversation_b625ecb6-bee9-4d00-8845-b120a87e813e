"""
DSPy evaluation framework for prompt optimization and performance assessment.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import random

import dspy
from dspy.evaluate import Evaluate
from dspy.teleprompt import BootstrapFewShot, MIPRO


class OSINTEvaluationMetric(dspy.Signature):
    """DSPy signature for evaluating OSINT analysis quality."""
    
    query = dspy.InputField(desc="Original OSINT query")
    analysis = dspy.InputField(desc="Generated analysis to evaluate")
    ground_truth = dspy.InputField(desc="Expected or reference analysis")
    
    accuracy_score = dspy.OutputField(desc="Accuracy score (0-10)")
    completeness_score = dspy.OutputField(desc="Completeness score (0-10)")
    relevance_score = dspy.OutputField(desc="Relevance score (0-10)")
    confidence_assessment = dspy.OutputField(desc="Assessment of confidence indicators")
    improvement_suggestions = dspy.OutputField(desc="Suggestions for improvement")


class GeopoliticalAnalysisEvaluator(dspy.Module):
    """Evaluator for geopolitical analysis quality."""
    
    def __init__(self):
        super().__init__()
        self.evaluate_analysis = dspy.ChainOfThought(OSINTEvaluationMetric)
    
    def forward(self, query, analysis, ground_truth=""):
        """Evaluate a geopolitical analysis."""
        return self.evaluate_analysis(
            query=query,
            analysis=analysis,
            ground_truth=ground_truth
        )


class CTIAnalysisEvaluator(dspy.Module):
    """Evaluator for CTI analysis quality."""
    
    def __init__(self):
        super().__init__()
        self.evaluate_analysis = dspy.ChainOfThought(OSINTEvaluationMetric)
    
    def forward(self, query, analysis, ground_truth=""):
        """Evaluate a CTI analysis."""
        return self.evaluate_analysis(
            query=query,
            analysis=analysis,
            ground_truth=ground_truth
        )


class DSPyEvaluator:
    """
    DSPy-based evaluation framework for OSINT agents.
    
    Provides prompt optimization, performance evaluation, and
    continuous improvement capabilities for OSINT analysis tasks.
    """
    
    def __init__(
        self,
        llm_model: str = "gpt-4",
        temperature: float = 0.1,
        verbose: bool = True
    ):
        """
        Initialize the DSPy evaluator.
        
        Args:
            llm_model: LLM model to use for evaluation
            temperature: LLM temperature setting
            verbose: Enable verbose logging
        """
        self.llm_model = llm_model
        self.temperature = temperature
        self.verbose = verbose
        self.logger = logging.getLogger(__name__)
        
        # Configure DSPy
        lm = dspy.OpenAI(model=llm_model, temperature=temperature)
        dspy.settings.configure(lm=lm)
        
        # Initialize evaluators
        self.geo_evaluator = GeopoliticalAnalysisEvaluator()
        self.cti_evaluator = CTIAnalysisEvaluator()
        
        # Evaluation datasets
        self.geo_dataset = []
        self.cti_dataset = []
        
        # Optimized modules
        self.optimized_modules = {}
    
    def create_evaluation_dataset(
        self,
        domain: str,
        examples: List[Dict[str, Any]]
    ) -> List[dspy.Example]:
        """
        Create an evaluation dataset for a specific domain.
        
        Args:
            domain: Domain type ('geopolitical' or 'cti')
            examples: List of example dictionaries with query, analysis, ground_truth
            
        Returns:
            List of DSPy examples
        """
        dataset = []
        
        for example in examples:
            dspy_example = dspy.Example(
                query=example.get("query", ""),
                analysis=example.get("analysis", ""),
                ground_truth=example.get("ground_truth", "")
            ).with_inputs("query", "analysis")
            
            dataset.append(dspy_example)
        
        # Store dataset
        if domain == "geopolitical":
            self.geo_dataset = dataset
        elif domain == "cti":
            self.cti_dataset = dataset
        
        self.logger.info(f"Created {domain} evaluation dataset with {len(dataset)} examples")
        return dataset
    
    def evaluate_analysis_quality(
        self,
        domain: str,
        query: str,
        analysis: str,
        ground_truth: str = ""
    ) -> Dict[str, Any]:
        """
        Evaluate the quality of an OSINT analysis.
        
        Args:
            domain: Domain type ('geopolitical' or 'cti')
            query: Original query
            analysis: Generated analysis
            ground_truth: Reference analysis (optional)
            
        Returns:
            Evaluation results with scores and feedback
        """
        try:
            # Select appropriate evaluator
            if domain == "geopolitical":
                evaluator = self.geo_evaluator
            elif domain == "cti":
                evaluator = self.cti_evaluator
            else:
                raise ValueError(f"Unknown domain: {domain}")
            
            # Perform evaluation
            evaluation = evaluator(
                query=query,
                analysis=analysis,
                ground_truth=ground_truth
            )
            
            # Parse scores
            try:
                accuracy = float(evaluation.accuracy_score)
                completeness = float(evaluation.completeness_score)
                relevance = float(evaluation.relevance_score)
            except (ValueError, TypeError):
                accuracy = completeness = relevance = 0.0
            
            # Calculate overall score
            overall_score = (accuracy + completeness + relevance) / 3
            
            result = {
                "domain": domain,
                "query": query,
                "overall_score": round(overall_score, 2),
                "scores": {
                    "accuracy": accuracy,
                    "completeness": completeness,
                    "relevance": relevance
                },
                "confidence_assessment": evaluation.confidence_assessment,
                "improvement_suggestions": evaluation.improvement_suggestions,
                "evaluation_timestamp": datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error evaluating analysis: {str(e)}")
            return {
                "error": str(e),
                "domain": domain,
                "query": query
            }
    
    def optimize_prompts(
        self,
        domain: str,
        module_to_optimize: dspy.Module,
        max_bootstrapped_demos: int = 4,
        max_labeled_demos: int = 16
    ) -> Tuple[dspy.Module, Dict[str, Any]]:
        """
        Optimize prompts using DSPy's optimization techniques.
        
        Args:
            domain: Domain type for selecting dataset
            module_to_optimize: DSPy module to optimize
            max_bootstrapped_demos: Maximum bootstrapped demonstrations
            max_labeled_demos: Maximum labeled demonstrations
            
        Returns:
            Optimized module and optimization results
        """
        try:
            # Select dataset
            if domain == "geopolitical":
                dataset = self.geo_dataset
            elif domain == "cti":
                dataset = self.cti_dataset
            else:
                raise ValueError(f"Unknown domain: {domain}")
            
            if not dataset:
                raise ValueError(f"No evaluation dataset available for {domain}")
            
            # Split dataset
            train_size = int(0.8 * len(dataset))
            trainset = dataset[:train_size]
            testset = dataset[train_size:]
            
            if len(trainset) == 0:
                raise ValueError("Training set is empty")
            
            # Define evaluation metric
            def evaluation_metric(example, pred, trace=None):
                """Custom evaluation metric for OSINT analysis."""
                try:
                    # Simple scoring based on content similarity and completeness
                    analysis = pred.analysis if hasattr(pred, 'analysis') else str(pred)
                    
                    # Basic scoring (in production, use more sophisticated metrics)
                    if len(analysis) > 100:  # Minimum length check
                        return 1.0
                    else:
                        return 0.0
                except:
                    return 0.0
            
            # Initialize optimizer
            optimizer = BootstrapFewShot(
                metric=evaluation_metric,
                max_bootstrapped_demos=max_bootstrapped_demos,
                max_labeled_demos=max_labeled_demos
            )
            
            # Optimize the module
            self.logger.info(f"Starting prompt optimization for {domain}")
            optimized_module = optimizer.compile(
                module_to_optimize,
                trainset=trainset
            )
            
            # Evaluate optimized module
            evaluator = Evaluate(
                devset=testset,
                metric=evaluation_metric,
                num_threads=1,
                display_progress=True
            )
            
            baseline_score = evaluator(module_to_optimize)
            optimized_score = evaluator(optimized_module)
            
            # Store optimized module
            self.optimized_modules[domain] = optimized_module
            
            optimization_results = {
                "domain": domain,
                "baseline_score": baseline_score,
                "optimized_score": optimized_score,
                "improvement": optimized_score - baseline_score,
                "training_examples": len(trainset),
                "test_examples": len(testset),
                "optimization_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"Optimization complete. Improvement: {optimization_results['improvement']:.3f}")
            
            return optimized_module, optimization_results
            
        except Exception as e:
            self.logger.error(f"Error optimizing prompts: {str(e)}")
            return module_to_optimize, {"error": str(e)}
    
    def run_comparative_evaluation(
        self,
        domain: str,
        modules: Dict[str, dspy.Module],
        test_queries: List[str]
    ) -> Dict[str, Any]:
        """
        Run comparative evaluation of multiple modules.
        
        Args:
            domain: Domain type
            modules: Dictionary of module name to module
            test_queries: List of test queries
            
        Returns:
            Comparative evaluation results
        """
        try:
            results = {}
            
            for module_name, module in modules.items():
                module_results = []
                
                for query in test_queries:
                    try:
                        # Generate analysis using the module
                        if hasattr(module, 'forward'):
                            analysis = module.forward(query=query)
                        else:
                            analysis = module(query=query)
                        
                        # Evaluate the analysis
                        evaluation = self.evaluate_analysis_quality(
                            domain=domain,
                            query=query,
                            analysis=str(analysis)
                        )
                        
                        module_results.append(evaluation)
                        
                    except Exception as e:
                        self.logger.warning(f"Error evaluating {module_name} on query '{query}': {str(e)}")
                        continue
                
                # Calculate average scores
                if module_results:
                    avg_scores = {
                        "overall": sum(r.get("overall_score", 0) for r in module_results) / len(module_results),
                        "accuracy": sum(r.get("scores", {}).get("accuracy", 0) for r in module_results) / len(module_results),
                        "completeness": sum(r.get("scores", {}).get("completeness", 0) for r in module_results) / len(module_results),
                        "relevance": sum(r.get("scores", {}).get("relevance", 0) for r in module_results) / len(module_results)
                    }
                    
                    results[module_name] = {
                        "average_scores": avg_scores,
                        "individual_results": module_results,
                        "queries_evaluated": len(module_results)
                    }
            
            # Rank modules by performance
            ranking = sorted(
                results.items(),
                key=lambda x: x[1]["average_scores"]["overall"],
                reverse=True
            )
            
            comparative_results = {
                "domain": domain,
                "modules_evaluated": list(modules.keys()),
                "test_queries": test_queries,
                "results": results,
                "ranking": [{"module": name, "score": data["average_scores"]["overall"]} for name, data in ranking],
                "evaluation_timestamp": datetime.now().isoformat()
            }
            
            return comparative_results
            
        except Exception as e:
            self.logger.error(f"Error in comparative evaluation: {str(e)}")
            return {"error": str(e)}
    
    def generate_synthetic_examples(
        self,
        domain: str,
        num_examples: int = 10,
        complexity_levels: List[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate synthetic examples for evaluation.
        
        Args:
            domain: Domain type
            num_examples: Number of examples to generate
            complexity_levels: List of complexity levels
            
        Returns:
            List of synthetic examples
        """
        if complexity_levels is None:
            complexity_levels = ["simple", "medium", "complex"]
        
        examples = []
        
        try:
            for i in range(num_examples):
                complexity = random.choice(complexity_levels)
                
                if domain == "geopolitical":
                    example = self._generate_geo_example(complexity)
                elif domain == "cti":
                    example = self._generate_cti_example(complexity)
                else:
                    continue
                
                examples.append(example)
            
            self.logger.info(f"Generated {len(examples)} synthetic examples for {domain}")
            return examples
            
        except Exception as e:
            self.logger.error(f"Error generating synthetic examples: {str(e)}")
            return []
    
    def _generate_geo_example(self, complexity: str) -> Dict[str, Any]:
        """Generate a synthetic geopolitical example."""
        regions = ["Eastern Europe", "Middle East", "Southeast Asia", "Africa", "Latin America"]
        topics = ["diplomatic relations", "trade disputes", "security concerns", "political developments"]
        
        region = random.choice(regions)
        topic = random.choice(topics)
        
        query = f"Analyze recent {topic} in {region}"
        
        # Generate a basic analysis structure
        analysis = f"""
        Analysis of {topic} in {region}:
        
        Current Situation: Recent developments indicate...
        Key Stakeholders: Primary actors include...
        Implications: The situation may lead to...
        Recommendations: Monitor for...
        """
        
        return {
            "query": query,
            "analysis": analysis,
            "ground_truth": "Reference analysis would be provided here",
            "complexity": complexity,
            "domain": "geopolitical"
        }
    
    def _generate_cti_example(self, complexity: str) -> Dict[str, Any]:
        """Generate a synthetic CTI example."""
        threat_types = ["APT", "ransomware", "malware", "phishing", "vulnerability"]
        targets = ["financial", "healthcare", "government", "critical infrastructure"]
        
        threat = random.choice(threat_types)
        target = random.choice(targets)
        
        query = f"Analyze {threat} threats targeting {target} sector"
        
        # Generate a basic analysis structure
        analysis = f"""
        Threat Analysis: {threat} targeting {target} sector
        
        Threat Overview: Recent campaigns show...
        Attribution: Evidence suggests...
        TTPs: Observed tactics include...
        IOCs: Key indicators are...
        Mitigation: Recommended defenses...
        """
        
        return {
            "query": query,
            "analysis": analysis,
            "ground_truth": "Reference analysis would be provided here",
            "complexity": complexity,
            "domain": "cti"
        }
    
    def export_evaluation_results(
        self,
        results: Dict[str, Any],
        filename: str = None
    ) -> str:
        """
        Export evaluation results to a file.
        
        Args:
            results: Evaluation results to export
            filename: Optional filename (auto-generated if not provided)
            
        Returns:
            Path to exported file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"evaluation_results_{timestamp}.json"
        
        try:
            filepath = f"./output/reports/{filename}"
            
            with open(filepath, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            self.logger.info(f"Evaluation results exported to: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting results: {str(e)}")
            return ""
