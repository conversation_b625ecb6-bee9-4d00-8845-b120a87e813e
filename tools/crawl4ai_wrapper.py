"""
Crawl4AI wrapper for structured web crawling and content extraction.
"""

import async<PERSON>
import json
from typing import Dict, List, Any, Optional
from langchain.tools import BaseTool
from pydantic import BaseModel, Field

try:
    from crawl4ai import AsyncWebCrawler
    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    AsyncWebCrawler = None


class Crawl4AIInput(BaseModel):
    """Input schema for Crawl4AI tool."""
    url: str = Field(description="URL to crawl and extract content from")
    focus_keywords: List[str] = Field(default=[], description="Keywords to focus extraction on")
    extract_links: bool = Field(default=True, description="Whether to extract links from the page")
    extract_images: bool = Field(default=False, description="Whether to extract image information")
    max_depth: int = Field(default=1, description="Maximum crawl depth")


class Crawl4AITool(BaseTool):
    """
    LangChain tool wrapper for Crawl4AI web crawling.
    
    Provides structured web crawling capabilities with keyword-focused
    content extraction for OSINT intelligence gathering.
    """
    
    name: str = "crawl4ai_web_crawler"
    description: str = """
    Crawl and extract structured content from web pages.
    Useful for gathering detailed information from specific websites,
    news articles, blogs, and other web sources. Can focus extraction
    on specific keywords and topics.
    """
    args_schema = Crawl4AIInput
    
    def __init__(self, focus_keywords: List[str] = None):
        """
        Initialize the Crawl4AI tool.
        
        Args:
            focus_keywords: Default keywords to focus on during extraction
        """
        super().__init__()
        self.default_focus_keywords = focus_keywords or []
        
        if not CRAWL4AI_AVAILABLE:
            raise ImportError(
                "Crawl4AI is not available. Install it with: pip install crawl4ai"
            )
    
    def _run(
        self,
        url: str,
        focus_keywords: List[str] = None,
        extract_links: bool = True,
        extract_images: bool = False,
        max_depth: int = 1
    ) -> str:
        """
        Crawl a web page and extract structured content.
        
        Args:
            url: URL to crawl
            focus_keywords: Keywords to focus extraction on
            extract_links: Whether to extract links
            extract_images: Whether to extract images
            max_depth: Maximum crawl depth
            
        Returns:
            JSON string with extracted content
        """
        try:
            # Use default keywords if none provided
            keywords = focus_keywords or self.default_focus_keywords
            
            # Run async crawling in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self._async_crawl(url, keywords, extract_links, extract_images, max_depth)
                )
                return json.dumps(result, indent=2)
            finally:
                loop.close()
                
        except Exception as e:
            return f"Error crawling URL {url}: {str(e)}"
    
    async def _async_crawl(
        self,
        url: str,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool,
        max_depth: int
    ) -> Dict[str, Any]:
        """Async crawling implementation."""
        
        async with AsyncWebCrawler(verbose=False) as crawler:
            # Configure crawling parameters
            crawl_config = {
                "word_count_threshold": 50,
                "extraction_strategy": "LLMExtractionStrategy",
                "chunking_strategy": "RegexChunking",
                "bypass_cache": True
            }
            
            # Add keyword-focused extraction if keywords provided
            if keywords:
                crawl_config["css_selector"] = None  # Let it extract everything first
                crawl_config["extraction_strategy"] = "LLMExtractionStrategy"
            
            # Perform the crawl
            result = await crawler.arun(
                url=url,
                **crawl_config
            )
            
            # Process and structure the results
            structured_result = self._process_crawl_result(
                result, keywords, extract_links, extract_images
            )
            
            return structured_result
    
    def _process_crawl_result(
        self,
        result: Any,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool
    ) -> Dict[str, Any]:
        """Process and structure crawl results."""
        
        structured = {
            "url": getattr(result, 'url', ''),
            "title": getattr(result, 'title', ''),
            "content": {
                "text": getattr(result, 'cleaned_html', ''),
                "markdown": getattr(result, 'markdown', ''),
                "word_count": len(getattr(result, 'cleaned_html', '').split())
            },
            "metadata": {
                "status_code": getattr(result, 'status_code', 0),
                "response_headers": getattr(result, 'response_headers', {}),
                "crawl_timestamp": getattr(result, 'timestamp', ''),
                "success": getattr(result, 'success', False)
            },
            "extraction": {}
        }
        
        # Extract links if requested
        if extract_links and hasattr(result, 'links'):
            structured["links"] = {
                "internal": getattr(result.links, 'internal', []),
                "external": getattr(result.links, 'external', [])
            }
        
        # Extract images if requested
        if extract_images and hasattr(result, 'media'):
            structured["images"] = getattr(result.media, 'images', [])
        
        # Perform keyword-focused extraction
        if keywords:
            structured["extraction"]["keyword_analysis"] = self._analyze_keywords(
                structured["content"]["text"], keywords
            )
        
        # Extract structured data if available
        if hasattr(result, 'extracted_content'):
            structured["extraction"]["structured_data"] = result.extracted_content
        
        return structured
    
    def _analyze_keywords(self, text: str, keywords: List[str]) -> Dict[str, Any]:
        """Analyze keyword presence and context in the text."""
        text_lower = text.lower()
        analysis = {
            "keyword_matches": {},
            "total_matches": 0,
            "keyword_density": {},
            "relevant_sentences": []
        }
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            matches = text_lower.count(keyword_lower)
            analysis["keyword_matches"][keyword] = matches
            analysis["total_matches"] += matches
            
            # Calculate keyword density
            word_count = len(text.split())
            if word_count > 0:
                analysis["keyword_density"][keyword] = (matches / word_count) * 100
            
            # Extract sentences containing the keyword
            sentences = text.split('.')
            relevant = [
                sentence.strip() for sentence in sentences 
                if keyword_lower in sentence.lower() and len(sentence.strip()) > 20
            ]
            analysis["relevant_sentences"].extend(relevant[:3])  # Limit to 3 per keyword
        
        return analysis
    
    async def _arun(self, *args, **kwargs) -> str:
        """Async version of the crawl tool."""
        return await self._async_crawl(*args, **kwargs)


class FocusedNewsCrawler(Crawl4AITool):
    """Specialized crawler for news websites."""
    
    name: str = "focused_news_crawler"
    description: str = """
    Crawl news websites and extract article content with focus on
    specific topics or keywords. Optimized for news article structure.
    """
    
    def __init__(self):
        super().__init__(focus_keywords=[
            "breaking", "news", "report", "analysis", "investigation",
            "sources", "officials", "statement", "confirmed"
        ])
    
    def _process_crawl_result(self, result, keywords, extract_links, extract_images):
        """Enhanced processing for news articles."""
        structured = super()._process_crawl_result(result, keywords, extract_links, extract_images)
        
        # Add news-specific extraction
        text = structured["content"]["text"]
        structured["extraction"]["news_analysis"] = {
            "article_type": self._classify_article_type(text),
            "key_entities": self._extract_entities(text),
            "publication_indicators": self._find_publication_info(text),
            "credibility_indicators": self._assess_credibility(text)
        }
        
        return structured
    
    def _classify_article_type(self, text: str) -> str:
        """Classify the type of news article."""
        text_lower = text.lower()
        
        if any(word in text_lower for word in ["breaking", "urgent", "alert"]):
            return "breaking_news"
        elif any(word in text_lower for word in ["analysis", "opinion", "editorial"]):
            return "analysis"
        elif any(word in text_lower for word in ["investigation", "exclusive", "revealed"]):
            return "investigative"
        else:
            return "general_news"
    
    def _extract_entities(self, text: str) -> List[str]:
        """Extract key entities from the text."""
        # Simple entity extraction - in production, use NLP libraries
        entities = []
        
        # Look for capitalized words that might be entities
        words = text.split()
        for i, word in enumerate(words):
            if (word[0].isupper() and len(word) > 2 and 
                not word.isupper() and word.isalpha()):
                # Check if it's likely a proper noun
                if i == 0 or not words[i-1].endswith('.'):
                    entities.append(word)
        
        # Remove duplicates and return top entities
        return list(set(entities))[:10]
    
    def _find_publication_info(self, text: str) -> Dict[str, Any]:
        """Find publication date, author, and source information."""
        return {
            "has_byline": "by " in text.lower(),
            "has_dateline": any(month in text.lower() for month in [
                "january", "february", "march", "april", "may", "june",
                "july", "august", "september", "october", "november", "december"
            ]),
            "has_source_attribution": any(phrase in text.lower() for phrase in [
                "according to", "sources say", "reported by", "confirmed by"
            ])
        }
    
    def _assess_credibility(self, text: str) -> Dict[str, Any]:
        """Assess credibility indicators in the article."""
        text_lower = text.lower()
        
        return {
            "has_quotes": '"' in text,
            "has_official_sources": any(phrase in text_lower for phrase in [
                "official", "spokesperson", "government", "ministry", "department"
            ]),
            "has_verification": any(phrase in text_lower for phrase in [
                "verified", "confirmed", "authenticated", "fact-checked"
            ]),
            "speculation_indicators": any(phrase in text_lower for phrase in [
                "allegedly", "reportedly", "rumored", "unconfirmed", "speculation"
            ])
        }
